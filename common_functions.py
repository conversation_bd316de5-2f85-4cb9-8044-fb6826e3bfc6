import os, sys
sys.path.insert(0, '/bi-python-tools')
sys.path.insert(0, '/workspace/bi-python-tools')

import pandas as pd
import numpy as np
import Database_Connection_Class as db
import general_tools as gt
import requests
from urllib.request import urlretrieve
from datetime import datetime
import time
import pickle
import json
creds = {'bi_admin':'work011DAY'}
conn = db.DBConnection(mars_cred=creds, 
                         venus_cred=creds,
                         panda_cred=creds,  
                         rs_cred=creds,
                          rs_sslmode = 'verify-ca')
ACCESS_KEY = '********************'
SECRET_KEY = '22ueDKaakUr0O3/w/Sln60v+O7w1EuNL7Nx6PhLv'
import redis
redis_db = redis.Redis(host='delorean.dressbad.com', port=6379, decode_responses=True, max_connections=200)
raw_redis_db = redis.Redis(host='delorean.dressbad.com', port=6379, decode_responses=False, max_connections=200)


###########################
## Product Daily Refresh ##
###########################

def get_prod_aff(siteflag = 'R'):
    '''
    runs once a day in the plp_pull_1d.py script
    pulls available product and their affinities/categories/brands
    '''
    q = f'''with lppvhpp_youngold_brands as( 
        select t1.brandname, t2.brandletter,
        (case when t1.lppvhpp = 'LPP' then 1.00 else 0.00 end) as LPP, 
        (case when t1.lppvhpp = 'HPP' then 1.00 else 0.00 end) as HPP,
        (case when t1.oldervyounger = 'Young' then 1.00 else 0.00 end) as Young,
        (case when t1.oldervyounger = 'Old' then 1.00 else 0.00 end) as Old,
        t3.old as Old2, t3.young as Young2
        from bi_work.mt_brandclustering_final t1
        right join bi_work.jr_brand_abbreviations t2
        on lower(t1.brandname) = lower(t2.brandname)
        right join bi_work.jr_age37_affinity t3
        on t2.brandletter = t3.brand
        )
    select t1.code, lower(t1.brandname) as brandname, split_part(t1.code, '-', 1) as brandletter, 
        case when t1.department in ('B', 'G', 'K') then 'K' else t1.department end as dept,
       lower(t1.cat1) as cat1, lower(t1.cat2) as cat2, lower(t1.cat3) as cat3, lower(t1.cat4) as cat4, lower(t1.cat5) as cat5, lower(t1.cat6) as cat6,
        CASE
        WHEN attributes IS NOT NULL 
             AND attributes != '' 
             AND attributes LIKE '{{%'
             THEN json_extract_path_text(attributes, 'subclass2type') 
        ELSE 
            NULL 
        END AS subclass2type,
        lower(t1.newforwardcategory1) as newforwardcategory1, lower(t1.newforwardcategory2) as newforwardcategory2, lower(t1.newforwardcategory3) as newforwardcategory3, lower(t1.newforwardcategory4) as newforwardcategory4, lower(t1.newforwardcategory5) as newforwardcategory5, lower(t1.newforwardcategory6) as newforwardcategory6,
        lower(t1.subbrand) as subbrand, lower(t1.brandcategory) as brandcategory,
        case when lower(trim(t1.finalsale)) in ('finalesale','finale Sale','final Sale','finalsale') then 1 else 0 end as finalsale,
        t1.readydate, coalesce(t2.LPP, 0) as LPP, coalesce(t2.HPP, 0) as HPP, 
        (case when t2.HPP = 0 and t2.LPP= 0 then 1.00 else 0.00 end) as "HPP+LPP",
        coalesce(t3.seg_fg,0) as seg_fg, coalesce(t3.seg_cg, 0)as seg_cg, coalesce(t3.seg_sc,0) as seg_sc,
        (case when t1.price < t1.retailprice then 1.00 ELSE 0.00 END) AS markdown,
        (case when t1.price = t1.retailprice then 1.00 ELSE 0.00 END) AS full_price,
        coalesce(t2.Young, 0) as Young,
        coalesce(t2.Old, 0) as Old,
        (case when t2.Young = 0 and t2.Old= 0 then 1.00 else 0.00 end) as "Young+Old",
        coalesce(t2.Young2, 0) as Young2,
        coalesce(t2.Old2, 0) as Old2,
        (case when t2.Young2 = 0 and t2.Old2 = 0 then 1.00 else 0.00 end) as "Young+Old2"
        FROM mars__revolveclothing_com___db.product t1 
        left join lppvhpp_youngold_brands t2
        on split_part(t1.code, '-', 1) = t2.brandletter
        left join bi_work.jr_brand_segment_mapping t3
        on t2.brandletter = t3.brandletter
        where t1.iscurrent = 1 and (t1.siteflag = '{siteflag}' or UPPER(t1.multisiteflags) LIKE '{siteflag},%') and upper(t1.code) not like 'GIFT%' and upper(t1.code) not like 'RALT%';'''.replace('%', '%%')
    prods = conn.getDfFromRedshift('products', q)
    return prods
def get_lettercat():
    '''
    pulls the lettercat and category mapping once a day
    '''
    q1 = '''select * from mars__id.id_categorynames2;'''
    lettercat = conn.getDfFromRedshift('lettercat', q1)
    return lettercat

def get_boost_factor():
    '''
    pulls the boost factor once a day
    '''
    q = "select productcode as product, boost_factor from bi.plp_sort_boost_factor where site = 'R'"
    df = conn.getDfFromPanda('boost factor', q)
    return df 

###################################
## Size Availability Calculation ##
###################################

def combine_sizes(row):
    '''
    combines the in-transit preorder sizes and preorder sizes
    '''
    # Get preorder sizes (from all_size_df2)
    preorder = str(row['preordersize']).strip(',') if pd.notna(row['preordersize']) else ''
    
    # Get in-transit sizes (from df_transit)
    transit = str(row['in_transit_sizes']) if pd.notna(row['in_transit_sizes']) else ''
    
    # Combine sizes and remove duplicates
    all_sizes = []
    if preorder:
        all_sizes.extend(preorder.split(','))
    if transit:
        all_sizes.extend(transit.split(','))
    
    # Remove empty strings and duplicates, then sort
    all_sizes = sorted(list(set(size.strip() for size in all_sizes if size.strip())))
    
    # Return comma-separated string with trailing comma
    return ','.join(all_sizes) + ',' if all_sizes else ''

def get_inventory_preorder():
    '''
    extracts products that are not oos (either in stock or preorder)
    runs every 20 minutes
    '''

    q = '''SELECT UPPER(i.product) AS product, p.scale, p.preordersize,
        GROUP_CONCAT(i.option1 SEPARATOR ',') AS all_size,
        GROUP_CONCAT(CASE WHEN sellableqty > 0 THEN i.option1 END ORDER BY i.option1 SEPARATOR ',') AS all_instock_size
        FROM `revolveclothing_com_-_db`.inventory i 
        JOIN `revolveclothing_com_-_db`.product p 
        ON p.code = i.product 
        WHERE p.iscurrent = 1 
        AND (p.siteflag = 'R' OR LOWER(p.multisiteflags) LIKE 'r,%') 
        AND (p.preordersize NOT IN ('None', '') OR EXISTS (
        SELECT 1 
        FROM `revolveclothing_com_-_db`.inventory i_sub
        WHERE i_sub.product = i.product AND i_sub.sellableqty > 0
    ))
        GROUP BY UPPER(i.product), p.scale, p.preordersize;'''.replace('%', '%%')
            
    all_size_df1 = conn.getDfFromPanda('all sizes', q)
    q_transit = '''
        SELECT 
            UPPER(p.code) AS product, p.scale,
            GROUP_CONCAT(DISTINCT i.size ORDER BY i.size SEPARATOR ',') AS in_transit_sizes,
            GROUP_CONCAT(DISTINCT inv.option1 ORDER BY inv.option1 SEPARATOR ',') AS all_size
        FROM `revolveclothing_com_-_db`.inventoryitem i
        JOIN `revolveclothing_com_-_db`.warehouselocation w ON i.location = w.location
        JOIN `revolveclothing_com_-_db`.product p ON i.product = p.code
        JOIN `revolveclothing_com_-_db`.inventory inv ON i.product = inv.product
        WHERE (p.siteflag = 'R' OR LOWER(p.multisiteflags) LIKE 'r,%')
        AND w.preorder = 1
        AND i.qtyonhand = 1
        AND p.iscurrent = 1
        GROUP BY UPPER(p.code), p.scale;'''.replace('%', '%%')

    df_transit = conn.getDfFromPanda('transit_check', q_transit)
    df_transit = df_transit.rename(columns={'all_size': 'transit_all_size', 'scale': 'transit_scale'})
    all_size_df = all_size_df1.merge(
        df_transit[['product', 'in_transit_sizes', 'transit_all_size', 'transit_scale']], 
        on='product', 
        how='outer'
    )

    # Use coalesce to get all_size and scale from either source
    all_size_df['all_size'] = all_size_df['all_size'].combine_first(all_size_df['transit_all_size'])
    all_size_df['scale'] = all_size_df['scale'].combine_first(all_size_df['transit_scale'])

    # Drop temporary columns after merge
    all_size_df = all_size_df.drop(['transit_all_size', 'transit_scale'], axis=1)

    # Combine the sizes
    all_size_df['combined_preordersize'] = all_size_df.apply(combine_sizes, axis=1)
    all_size_df['cat'] = all_size_df['product'].apply(lambda x: x.split('-')[1][1])
    # Update preordersize with combined sizes
    all_size_df['preordersize'] = all_size_df['combined_preordersize']
    # Drop temporary columns
    all_size_df = all_size_df.drop(['in_transit_sizes', 'combined_preordersize'], axis=1)
    all_size_df = all_size_df.fillna(value='')
    return all_size_df


def custom_sort(sizes, is_shoe):
    '''sort numerical sizes in ascending order and letter standard size in XL-XS order'''
    if is_shoe:
        # Sort numerical sizes in ascending order
        try:
            return sorted(sizes, key=lambda x: float(x) if x.replace('.', '', 1).isdigit() else x)
        except:
            return ''
    else:
        # Sort letter sizes in the order of XL, L, M, S, XS
        order = {'XL': 0, 'L': 1, 'M': 2, 'S': 3, 'XS': 4}
        return sorted(sizes, key=lambda x: order.get(x, 5))

def get_std_mapping():
    '''pulls the standard size mapping'''
    q = '''select * from `revolveclothing_com_-_db`.sizetostandardsizemapping;'''
    standardsizemapping = conn.getDfFromPanda('standard size mapping', q)
    return standardsizemapping

def get_sa_mapping():
    '''pulls the size availability category weight mapping'''
    q2 = "select category, sizeavailability, atcrate_weight_avg as sa_weight from bi.mw_atcrate_final;"
    sa_weight = conn.getDfFromPanda('sa_weight', q2) 
    return sa_weight

def map_sizes(sizes, mapping_df, scale, is_shoe):
    ''' 
    sizes: a string of comma-separated sizes
    mapping_df: standard size mapping df
    scale: the scale for sizes
    is_shoe: whether this product is shoes or not
    '''
    if sizes in (None, '', 'None', np.nan):
        sizes = ''
    size_list = [size.strip() for size in sizes.split(',') if size.strip()]
    # used to store the mapped sizes
    mapped_sizes_num = []
    mapped_sizes_std = []
    
    missing_scale_list = ['UK3 / US Womens 5 - UK10 / US Womens 12',
       'UK4 (US Womens 5) - UK12 (US Womens 13)',
       'UK 3.5 (US Mens 4 / Womens 5) - UK 13 (US Mens 13.5 / Womens 13)',
       'Mens 4 / Womens 5 - Mens 13 / Womens 14',
       'M4.5 / W5.5 - M13 / W14',
       'Unisex Mens 3/Womens 4.5 - Mens 13/Womens 14.5',
       '24 (6-6.5) - 37 (6-6.5)', '35 (US 4-4.5) - 42 (US 11-11.5)',
       '4/4.5 - 14/14.5', 'M3 / W5 - M12 / W14', 'M3 / W4.5 - M13',
       'Mens 3.5 / Womens 5.5 - Mens 13 / Womens 15', 'M4 / W5.5 - M14',
       '35-38 (S/M) - 39-41 (M/L)', '36-38 - 39-41', '33/35 - 45/47',
       '33/35 - 42/44', 'EUR 36-48 2/3', '6C - 7Y']
    
    if sizes: # if the sizes is empty, just return empty, no matter the scale is unknown or all
        # Handle missing scale case
        if scale in missing_scale_list:
            mapped_sizes_std = ['Unknown']
            mapped_sizes_num = ['Unknown']
        else:    
            # Filter the mapping dataframe by the correct scale
            relevant_mapping_df = mapping_df[mapping_df['scale'] == scale]

            for size in size_list:
                # for one-size items, e.g. U, A
                if size.lower() == 'all':
                    mapped_sizes_std.append('all')
                    mapped_sizes_num.append('all')
                else:
                    # map all products in two ways, but only shoes will later use numerical mapping
                    mapped_num = relevant_mapping_df[relevant_mapping_df['size'] == size]['mapping'].values
                    mapped_let = relevant_mapping_df[relevant_mapping_df['size'] == size]['standardsize'].values
                    
                    if len(mapped_num) > 0:
                        mapped_sizes_num.append(mapped_num[0])
                    if len(mapped_let) > 0:
                        mapped_sizes_std.append(mapped_let[0])
        
    return sorted(set(mapped_sizes_std)), sorted(set(mapped_sizes_num))

def process_and_calculate_availability(row, mapping_df):
    '''
    calculate and standardize size availability format
    '''
    # Determine if the product is a shoe
    is_shoe = (row['cat'] == 'Z')
    
    # Extract the relevant scale for this row
    scale = row['scale']
    
    # Map the sizes once for all columns
    preorder_std, preorder_std_num = map_sizes(row['preordersize'], mapping_df, scale, is_shoe)
    all_size_std, all_size_std_num = map_sizes(row['all_size'], mapping_df, scale, is_shoe)
    all_instock_size_std, all_instock_size_std_num = map_sizes(row['all_instock_size'], mapping_df, scale, is_shoe)
    
    # Create the standardized size columns
    row['preorder_std'] = ', '.join(preorder_std)
    row['preorder_std_num'] = ','.join(preorder_std_num)
    row['all_size_std'] = ', '.join(all_size_std)
    row['all_size_std_num'] = ', '.join(all_size_std_num)
    row['all_instock_size_std'] = ', '.join(all_instock_size_std)
    row['all_instock_size_std_num'] = ', '.join(all_instock_size_std_num)

    
    # Calculate availability based on standardized sizes
    if not all_instock_size_std and not preorder_std:
         row['size_availability'] = 'No XL L M S XS'
         row['total_size_availability'] = 'No XL L M S XS'
    elif not all_instock_size_std:
        row['size_availability'] = 'No XL L M S XS' 
        if 'Unknown' in preorder_std:
            row['total_size_availability'] = 'Unknown Sizes Instock'
        elif 'all' in preorder_std:
            row['total_size_availability'] = 'Full Stock'
        else: 
            total_size_availability = custom_sort(set(all_size_std) - set(preorder_std), False)
    else: # all instock is not None
        # if 'all' in all_instock_size_std: #'all - all = size availability length 0'
        #     row['size_availability'] = 'Full Stock'
        #     row['total_size_availability'] = 'Full Stock'
            # unknown sizes
        if 'Unknown' in all_instock_size_std:
            row['size_availability'] = 'Unknown Sizes Instock'
            row['total_size_availability'] = 'Unknown Sizes Instock'
        # check the difference 
        else:
            size_availability = custom_sort(set(all_size_std) - set(all_instock_size_std), False)
            total_size_availability = custom_sort(set(all_size_std) - set(all_instock_size_std + preorder_std), False)
            row['size_availability'] = 'Full Stock' if len(size_availability) == 0 else f"No {' '.join(size_availability)}"
            row['total_size_availability'] = 'Full Stock' if len(total_size_availability) == 0 else f"No {' '.join(total_size_availability)}"
        
    if is_shoe:
        if not all_instock_size_std and not preorder_std:
            row['size_availability_num'] = 'OOS'
            row['total_size_availability_num'] = 'OOS'
        elif not all_instock_size_std:
            row['size_availability_num'] = 'OOS' 
        elif 'Unknown' in all_instock_size_std:
            row['size_availability_num'] = 'Unknown Sizes Instock' 
            row['total_size_availability_num'] = 'Unknown Sizes Instock'
        elif 'Unknown' in preorder_std:
            row['total_size_availability_num'] = 'Unknown Sizes Instock'
                
        else:
            size_availability_num = custom_sort(set(all_size_std_num) - set(all_instock_size_std_num), True)
            total_size_availability_num = custom_sort(set(all_size_std_num) - set(all_instock_size_std_num + preorder_std_num), True)
            row['size_availability_num'] = 'Full Stock' if len(size_availability_num) == 0 else f"No {' '.join(size_availability_num)}"
            row['total_size_availability_num'] = 'Full Stock' if len(total_size_availability_num) == 0 else f"No {' '.join(total_size_availability_num)}"
            
    return row

def get_size_availability(prods, all_size_df, standardsizemapping, lettercat, sa_weight):
    '''
    calculate size availability score 
    '''
    all_size_df = all_size_df.apply(lambda row: process_and_calculate_availability(row, standardsizemapping), axis=1)
    size_avail_df = all_size_df.merge(lettercat, how = 'left', left_on = 'cat', right_on = 'lettercat')
    size_avail_df = size_avail_df[['product', 'size_availability', 'total_size_availability', 'size_availability_num', 'total_size_availability_num','lettercat', 'catname2', 'catname1']]
    category_mapper = {
        'Tops': 'Tops',
        'Bottoms': 'Bottoms',
        'Dresses': 'Dresses',
        'Outerwear': 'All', 
        'Shoes': 'All',
        'Activewear': 'All',
        'Beauty': 'All',
        'Handbags': 'All',
        'eGiftCertificates': 'All',
        'Accessories': 'All'
        
    }
    size_avail_df['category'] = size_avail_df['catname1'].map(category_mapper)
    size_avail_df = size_avail_df.merge(sa_weight, how = 'left', left_on = ['category', 'size_availability'], right_on = ['category', 'sizeavailability'])
    size_avail_df=size_avail_df[['product', 'size_availability', 'total_size_availability',  'size_availability_num', 'total_size_availability_num','lettercat',
        'catname2', 'catname1', 'category',  'sa_weight']]
    size_avail_df.rename(columns={'sa_weight': 'wop'}, inplace = True)
    size_avail_df = size_avail_df.merge(sa_weight, how = 'left', left_on = ['category', 'total_size_availability'], right_on = ['category', 'sizeavailability'])
    size_avail_df=size_avail_df[['product', 'size_availability', 'total_size_availability',  'size_availability_num', 'total_size_availability_num','lettercat',
        'catname2', 'catname1', 'category',  'wop', 'sa_weight']]
    size_avail_df.rename(columns={'sa_weight': 'wo'}, inplace = True)

    q_i = '''select product, proj_kr,capped_netrev_on_median_imp from bi.impressions_prodstats'''
    impression = conn.getDfFromPanda('impression', q_i)
    # keep all is current products, and if impression missing, use median(247) to replace
    size_avail_df = size_avail_df.merge(impression, how = 'left', on = 'product')
    #size_avail_df = size_avail_df[(size_avail_df['size_availability']!='No XL L M S XS') & (size_avail_df['total_size_availability']!='No XL L M S XS')].reset_index(drop = True)
    size_avail_df['capped_netrev_on_median_imp'] = size_avail_df['capped_netrev_on_median_imp'].fillna(388.743)
    size_avail_df.drop_duplicates(inplace = True)
    df = prods.merge(size_avail_df, how = 'right', left_on = 'code', right_on = 'product') # right join so that all products have impression scores
    ### data cleaning 
    # some products were on revolve before or they are out of stock hence not in inventory table
    df.dropna(subset = ['code'], inplace = True) 
    #- 'full stock' weight set to 1; 
    # 'unknown sizes in stock' weight set to 1, for easier matrix set up later, set 'unknown sizes in stock' to 'full stock'
    df['size_availability2'] = df['size_availability']
    df['total_size_availability2'] = df['total_size_availability']
    df.loc[df['size_availability'] == 'Unknown Sizes Instock', 'size_availability2'] = 'Full Stock'
    df.loc[df['total_size_availability'] == 'Unknown Sizes Instock', 'total_size_availability2'] = 'Full Stock'
    
    df['size_availability_num2'] = df['size_availability_num']
    df['total_size_availability_num2'] = df['total_size_availability_num']
    df.loc[df['size_availability_num'] == 'Unknown Sizes Instock', 'size_availability_num2'] = 'Full Stock'
    df.loc[df['total_size_availability_num'] == 'Unknown Sizes Instock', 'total_size_availability_num2'] = 'Full Stock'
    
    df.loc[df['size_availability'].isna(), 'size_availability2'] = 'Full Stock' # usually there isn't any
    df.loc[df['total_size_availability'].isna(), 'total_size_availability2'] = 'Full Stock' 
    
    df.loc[df['size_availability_num'].isna(), 'size_availability_num2'] = 'Full Stock' # usually there isn't any
    df.loc[df['total_size_availability_num'].isna(), 'total_size_availability_num2'] = 'Full Stock' 
    
    df.loc[df['size_availability2'] == 'Full Stock', 'wop'] = 1
    df.loc[df['total_size_availability2'] == 'Full Stock', 'wo'] = 1
    
    # add to card weight Wop + (Wo - Wop) * 0.6
    df['sa_weight'] = df['wop'] + (df['wo'] - df['wop']) * 0.6
    df = df.reset_index(drop = True) 
    cat_dict = {
            'D' : 'Clothing',
            'K' : 'Clothing',
            'M' : 'Clothing',
            'O' : 'Clothing',
            'S' : 'Clothing',
            'F' : 'Bottoms',
            'J' : 'Bottoms',
            'N' : 'Bottoms',
            'P' : 'Bottoms',
            'Q' : 'Bottoms',
            'R' : 'Bottoms',
            'Z' : 'Shoes'
        }

    df['cat'] = df['product'].apply(lambda x: x.split('-')[1][1])
    df['catname3'] = df['cat'].map(lambda x: cat_dict[x] if x in cat_dict.keys() else 'Others')
    catlist = ['Activewear', 'Bottoms', 'Outerwear', 'Dresses', 'Tops']
    df['Clothing'] = df['catname1'].isin(catlist)
    return df

def calculate_shoe_score(missing_sizes_str, ps_dict, cat_code):
    '''
    calculate shoe size availability score
    '''
    shoe_sa_list = ['No 5',
    'No 5.5',
    'No 6',
    'No 6.5',
    'No 7',
    'No 7.5',
    'No 8',
    'No 8.5',
    'No 9',
    'No 9.5',
    'No 10',
    'No 10.5',
    'No 11',
    'No 11.5',
    'No 12',
    'No 12.5',
    'No 13']
    if pd.isna(missing_sizes_str) or missing_sizes_str == 'Full Stock':
        return 0
    missing_sizes = missing_sizes_str.split()[1:]  # Skip the 'No' part
    total_score = 0
    for size in missing_sizes:
        try:
            size_key = shoe_sa_list.index(f'No {size}')
            total_score += ps_dict.get(cat_code + size_key, 0)
        except ValueError:
            # If the size is not in shoe_sa_list, assign a score of 0
            total_score += 0
    return total_score

################
## Product RR ##
################


def prod_exp_rr():
    '''
    get user prod rr buckets
    pulls once a day 
    '''
    # Query for domestic products
    q_domestic = '''
        SELECT
            productcode,
            1 as domestic,
            MAX(CASE WHEN cust_rr_units_bucket = -1 THEN user_prod_rr END) AS cust_rr_units_bucket_new,
            MAX(CASE WHEN cust_rr_units_bucket = 0 THEN user_prod_rr END) AS cust_rr_units_bucket_0,
            MAX(CASE WHEN cust_rr_units_bucket = 0.001 THEN user_prod_rr END) AS cust_rr_units_bucket_0_001,
            MAX(CASE WHEN cust_rr_units_bucket = 0.3 THEN user_prod_rr END) AS cust_rr_units_bucket_0_3,
            MAX(CASE WHEN cust_rr_units_bucket = 0.399 THEN user_prod_rr END) AS cust_rr_units_bucket_0_399,
            MAX(CASE WHEN cust_rr_units_bucket = 0.477 THEN user_prod_rr END) AS cust_rr_units_bucket_0_477,
            MAX(CASE WHEN cust_rr_units_bucket = 0.597 THEN user_prod_rr END) AS cust_rr_units_bucket_0_597,
            MAX(CASE WHEN cust_rr_units_bucket = 0.711 THEN user_prod_rr END) AS cust_rr_units_bucket_0_711,
            MAX(CASE WHEN cust_rr_units_bucket = 0.825 THEN user_prod_rr END) AS cust_rr_units_bucket_0_825
        from bi_work.user_prod_rr_domestic
        group by productcode
        ;'''
    
    # Query for non-domestic products
    q_nondomestic = '''
        SELECT
            productcode,
            0 as domestic,
            MAX(CASE WHEN cust_rr_units_bucket = -1 THEN user_prod_rr END) AS cust_rr_units_bucket_new,
            MAX(CASE WHEN cust_rr_units_bucket = 0 THEN user_prod_rr END) AS cust_rr_units_bucket_0,
            MAX(CASE WHEN cust_rr_units_bucket = 0.001 THEN user_prod_rr END) AS cust_rr_units_bucket_0_001,
            MAX(CASE WHEN cust_rr_units_bucket = 0.3 THEN user_prod_rr END) AS cust_rr_units_bucket_0_3,
            MAX(CASE WHEN cust_rr_units_bucket = 0.399 THEN user_prod_rr END) AS cust_rr_units_bucket_0_399,
            MAX(CASE WHEN cust_rr_units_bucket = 0.477 THEN user_prod_rr END) AS cust_rr_units_bucket_0_477,
            MAX(CASE WHEN cust_rr_units_bucket = 0.597 THEN user_prod_rr END) AS cust_rr_units_bucket_0_597,
            MAX(CASE WHEN cust_rr_units_bucket = 0.711 THEN user_prod_rr END) AS cust_rr_units_bucket_0_711,
            MAX(CASE WHEN cust_rr_units_bucket = 0.825 THEN user_prod_rr END) AS cust_rr_units_bucket_0_825
        from bi_work.user_prod_rr_domestic
        group by productcode
        ;'''
    
    # Get both dataframes
    prod_rr_df_domestic = conn.getDfFromRedshift('prod_rr_df_domestic', q_domestic)
    prod_rr_df_nondomestic = conn.getDfFromRedshift('prod_rr_df_nondomestic', q_nondomestic)
    
    return prod_rr_df_domestic, prod_rr_df_nondomestic


def get_coef():
    '''
    get the coefficients for the old rr model
    runs once a day 
    '''
    df_coef = conn.getDfFromPanda('coef',"""
                            select 
                            (select coefficient from bi.mlrr_oc_coefficients where variable = 'prod_exp_rr') prod_exp_rr_coe, 
                            (select coefficient from bi.mlrr_oc_coefficients where variable = 'finalsale') finalsale_coe, 
                            (select coefficient from bi.mlrr_oc_coefficients where variable = 'cust_rr_units') cust_rr_units_coe
                            """)
    return df_coef

# Old RR 
def partial_normalization(df, rr_coef):
    '''
    normalize the rr features (old rr model)
    '''
    for v in ['prod_exp_rr', 'finalsale']:
        for s in ['mean', 'std']:
            df[f'{v}_{s}'] = conn.getDfFromRedshift('normalization',f"select {s} from bi_work.mlrr_oc_normalization where variable = '{v}'").iloc[0,0]
        df[v + '_standized'] = (df[v].astype('float') - df[v + '_mean']) / df[v + '_std']
    cust_rr_df = conn.getDfFromRedshift('normalization',"select mean as cust_rr_units_mean, std as cust_rr_units_std from bi_work.mlrr_oc_normalization where variable = 'cust_rr_units'")
    df[['cust_rr_units_mean', 'cust_rr_units_std']] = cust_rr_df[['cust_rr_units_mean', 'cust_rr_units_std']].iloc[0]
    df['pre_load_rr_features'] = np.matmul(np.array(df[['prod_exp_rr_standized','finalsale_standized']]), np.array(rr_coef.iloc[0, :2]))
    return df 

# Old RR 
def get_rr_params(siteflag = 'R'):
    df_params = conn.getDfFromPanda('params',f"""
    select p.code, 
    metl.posterior_rr prod_exp_rr, metl.domestic
    from `revolveclothing_com_-_db`.product p
    join bi.mlrr_exprr_training_latest metl on metl.productcode = p.code
    where upper(p.code) not like 'GIFT%' and upper(p.code) not like 'RALT%' and p.iscurrent = 1 and p.instock = 1 
                                    and (p.siteflag = '{siteflag}' or UPPER(p.multisiteflags) LIKE '{siteflag},%') 
    """.replace('%', '%%'))
    return df_params



def prod_exp_rr_fillna(df):
    '''
    fill na values for prod_exp_rr
    '''
    q = '''select t1.catname, t2.brandletter, t1.brandcat_rr from bi_work.hist_brandcat_rr_ml_sx t1
    left join bi_work.jr_brand_abbreviations t2 on lower(t1.brandname) = lower(t2.brandname)
    where t2.brandname != '' and t2.brandname is not null;'''
    cat_rr1 = conn.getDfFromRedshift('cat rr', q)
    q = '''select catname, brandcat_rr from bi.hist_brandcat_rr_ml_sx where (brandname = '' or brandname is null) and brandcat_past_totalsales = 0;'''
    cat_rr2 = conn.getDfFromPanda('cat rr', q) 
    df = df.merge(cat_rr1, how = 'left', left_on = ['brandletter', 'catname1'], right_on = ['brandletter', 'catname']) 
    cols_to_fill = [
        'cust_rr_units_bucket_0', 'cust_rr_units_bucket_0_001',
        'cust_rr_units_bucket_0_3', 'cust_rr_units_bucket_0_399',
        'cust_rr_units_bucket_0_477', 'cust_rr_units_bucket_0_597',
        'cust_rr_units_bucket_0_711', 'cust_rr_units_bucket_0_825'
    ]
    for col in cols_to_fill:
        df[col] = df[col].fillna(df['brandcat_rr'])
    df = df.drop(['catname', 'brandcat_rr'], axis = 1)
    df = pd.merge(df, cat_rr2[['catname', 'brandcat_rr']], left_on='catname1', right_on='catname', how='left')
    for col in cols_to_fill:
        df[col] = df[col].fillna(df['brandcat_rr'])
    df = df.drop(['catname', 'brandcat_rr'], axis = 1)
    return df



##################
### real-time ####
##################

def get_affinity(browserid = None, site = 'R'):
    '''
    get customer's affinities 
    browserid: str
    site: str 
    '''
    browseridsite = browserid + site
    results = redis_db.get(f'aff_{browseridsite}')
    if results:
        return json.loads(results)
    return None






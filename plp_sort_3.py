import os, sys
sys.path.insert(0, '/bi-python-tools')
os.environ["OMP_NUM_THREADS"] = "3"
os.environ["NUMEXPR_NUM_THREADS"] = "3"
os.environ["MKL_NUM_THREADS"] = "3"

import pandas as pd
import numpy as np
import Database_Connection_Class as db
import general_tools as gt
import requests
from urllib.request import urlretrieve
from datetime import datetime
import time
import pickle
import json
import math
creds = {'bi_admin':'work011DAY'}
conn = db.DBConnection(mars_cred=creds, 
                         venus_cred=creds,
                         panda_cred=creds,  
                         rs_cred=creds,
                          rs_sslmode = 'verify-ca')
ACCESS_KEY = '********************'
SECRET_KEY = '22ueDKaakUr0O3/w/Sln60v+O7w1EuNL7Nx6PhLv'
from common_functions import *
import gunicorn
import uvicorn
import logging 
logging.basicConfig(
    filename='/workspace/logs/plp_sort_log.txt',
    format='%(asctime)s %(levelname)-8s %(message)s',
    level=logging.INFO,
    datefmt='%Y-%m-%d %H:%M:%S')
logger = logging.getLogger("uvicorn.error")

from fastapi import FastAPI, Request, Query
from fastapi.responses import PlainTextResponse
# from redis import Redis
# redis_db = redis.Redis(host='delorean.dressbad.com', port=6379, decode_responses=True)
###########################
### One Time Daily Pull ###
###########################

default_pw = {'lv': '1970-1-1',
 'ps_clo': {'No L': '0',
  'No L M': '0',
  'No L M S': '0',
  'No L M S XS': '0',
  'No L M XS': '0',
  'No L S': '0',
  'No L S XS': '0',
  'No L XS': '0',
  'No M': '0',
  'No M S': '0',
  'No M S XS': '0',
  'No M XS': '0',
  'No S': '0',
  'No S XS': '0',
  'No XL': '0',
  'No XL L': '0',
  'No XL L M': '0',
  'No XL L M S': '0',
  'No XL L M XS': '0',
  'No XL L S': '0',
  'No XL L S XS': '0',
  'No XL L XS': '0',
  'No XL M': '0',
  'No XL M S': '0',
  'No XL M S XS': '0',
  'No XL M XS': '0',
  'No XL S': '0',
  'No XL S XS': '0',
  'No XL XS': '0',
  'No XS': '0',
  'No XL L M S XS': '0'},
 'ps_bot': {'No L': '0',
  'No L M': '0',
  'No L M S': '0',
  'No L M S XS': '0',
  'No L M XS': '0',
  'No L S': '0',
  'No L S XS': '0',
  'No L XS': '0',
  'No M': '0',
  'No M S': '0',
  'No M S XS': '0',
  'No M XS': '0',
  'No S': '0',
  'No S XS': '0',
  'No XL': '0',
  'No XL L': '0',
  'No XL L M': '0',
  'No XL L M S': '0',
  'No XL L M XS': '0',
  'No XL L S': '0',
  'No XL L S XS': '0',
  'No XL L XS': '0',
  'No XL M': '0',
  'No XL M S': '0',
  'No XL M S XS': '0',
  'No XL M XS': '0',
  'No XL S': '0',
  'No XL S XS': '0',
  'No XL XS': '0',
  'No XS': '0',
  'No XL L M S XS': '0'},
 'ps_sho': {'No 5': '0',
  'No 5.5': '0',
  'No 6': '0',
  'No 6.5': '0',
  'No 7': '0',
  'No 7.5': '0',
  'No 8': '0',
  'No 8.5': '0',
  'No 9': '0',
  'No 9.5': '0',
  'No 10': '0',
  'No 10.5': '0',
  'No 11': '0',
  'No 11.5': '0',
  'No 12': '0',
  'No 12.5': '0',
  'No 13': '0'},
 'ps_aff': {'FP': '1',
  'MD': '1',
  'HPP': '1',
  'LPP': '1',
  'Young': '1',
  'Old': '1',
  'Young+Old': '1',
  'FemmeGirl': '1',
  'CoolGirl': '1',
  'SophisticatedChic': '1',
  'Old2': '1',
  'Young2': '1',
  'RR': '1'},
 'domestic': 1, 
 'visitorsegment': ''}
size_availability_list = [
    'Full Stock', 'No L', 'No L M', 'No L M S', 'No L M S XS', 'No L M XS', 'No L S',
    'No L S XS', 'No L XS', 'No M', 'No M S', 'No M S XS', 'No M XS', 'No S', 'No S XS',
    'No XL', 'No XL L', 'No XL L M', 'No XL L M S', 'No XL L M XS', 'No XL L S',
    'No XL L S XS', 'No XL L XS', 'No XL M', 'No XL M S', 'No XL M S XS', 'No XL M XS',
    'No XL S', 'No XL S XS', 'No XL XS', 'No XS', 'No XL L M S XS'
]
size_availability_dict = {v: i for i, v in enumerate(size_availability_list)}
shoe_sa_list = ['No 5',
'No 5.5',
'No 6',
'No 6.5',
'No 7',
'No 7.5',
'No 8',
'No 8.5',
'No 9',
'No 9.5',
'No 10',
'No 10.5',
'No 11',
'No 11.5',
'No 12',
'No 12.5',
'No 13']
shoe_sa_dict = {size: idx for idx, size in enumerate(shoe_sa_list)}  
type_idx_dict = {'clo' : 1, 'bot' : 2, 'sho' : 3}
catname_dict = {'Clothing' : 1, 'Bottoms' : 2, 'Shoes' : 3, 'Others' : 0}

rr_coef = pd.read_pickle('/workspace/pickle_files/rr_coef.pkl')
with open('/workspace/pickle_files/df_dict.pkl', 'rb') as handle:
    df_dict = pickle.load(handle)
with open('/workspace/pickle_files/shoe_sa_arr.pkl', 'rb') as handle:
    shoe_sa_arr = pickle.load(handle)
with open('/workspace/pickle_files/shoe_total_sa_arr.pkl', 'rb') as handle:
    shoe_total_sa_arr = pickle.load(handle)
with open('/workspace/pickle_files/size_indices.pkl', 'rb') as handle:
    size_indices = pickle.load(handle)
with open('/workspace/pickle_files/size_indices2.pkl', 'rb') as handle:
    size_indices2 = pickle.load(handle)
with open('/workspace/pickle_files/shoe_sa_arr.pkl', 'rb') as handle:
    shoe_sa_arr = pickle.load(handle)
with open('/workspace/pickle_files/shoe_total_sa_arr.pkl', 'rb') as handle:
    shoe_total_sa_arr = pickle.load(handle)
with open('/workspace/pickle_files/size_indices.pkl', 'rb') as handle:
    size_indices = pickle.load(handle)
with open('/workspace/pickle_files/size_indices2.pkl', 'rb') as handle:
    size_indices2 = pickle.load(handle)
with open('/workspace/pickle_files/size_indices2.pkl', 'rb') as handle:
    size_indices2 = pickle.load(handle)
with open('/workspace/pickle_files/cat_arr.pkl', 'rb') as handle:
    cat_arr = pickle.load(handle)
with open('/workspace/pickle_files/aff_values.pkl', 'rb') as handle:
    aff_values = pickle.load(handle)
    
df_dict['dom']['readydate'] = pd.to_datetime(df_dict['dom']['readydate'])
df_dict['intl']['readydate'] = pd.to_datetime(df_dict['intl']['readydate'])
# Convert these to datetimes up front, not in real-time since it's the same data transformation over and over

columns = ['product','Clothing','dept','catname1','brandletter','capped_netrev_on_median_imp','finalsale', 'readydate','proj_kr',
           'sa_weight','pre_load_rr_features', 'cust_rr_units_mean', 'cust_rr_units_std', 'calibration_ratio','boost_factor']
df_dict['dom'] = df_dict['dom'].reset_index(drop = True)[columns]
df_dict['intl'] = df_dict['intl'].reset_index(drop = True)[columns]

clothing_df_dict = {
    'dom': df_dict['dom'][df_dict['dom']['Clothing']].copy().reset_index(drop = True),
    'intl' : df_dict['intl'][df_dict['intl']['Clothing']].copy().reset_index(drop = True)
}

clothing_idx = {
    'dom' : np.where(df_dict['dom']['Clothing'])[0],
    'intl' : np.where(df_dict['intl']['Clothing'])[0]
}

#################
### real time ###
#################

def get_sort(df_dict, cat_arr, size_indices, size_indices2, shoe_sa_arr, shoe_total_sa_arr,aff_values, 
                browserid = 'none', dept = 'F', cat = None, brand_prefix = None, siteflag = 'R', rr = False,
               debug = False):
    
    # get customer's affinities
    pw = get_affinity(browserid, siteflag)
    
    if pw is None or len(pw) == 0:
        pw = default_pw
    try:
        dom = pw['domestic']
    except KeyError:
        dom = 1
    if dom == 1:
        dom = 'dom'
    else:
        dom = 'intl'
    
    for key in ['clo', 'bot', 'sho', 'aff']:
        if not pw[f'ps_{key}']:
            pw[f'ps_{key}'] = default_pw[f'ps_{key}']
    
    # Filtering
    # mask = None
    # if dept is not None and len(dept) > 0:
    #     mask = (df_dict[dom]['dept'] == dept)
    # if cat is not None and len(cat) > 0:
    #     if mask is None:
    #         mask = (df_dict[dom]['catname1'] == cat)
    #     else:
    #         mask = (mask & (df_dict[dom]['catname1'] == cat))
    # if brand_prefix is not None and len(brand_prefix) > 0:
    #     if mask is None:
    #         mask = (df_dict[dom]['brandletter'] == brand_prefix)
    #     else:
    #         mask = (mask & (df_dict[dom]['brandletter'] == brand_prefix))
    
    # if mask is not None:
    #     filter_idx = np.where(np.array(mask))[0]
    #     df2 = df_dict[dom][mask].copy()
    # else:
    #     filter_idx = np.s_[:]
    #     df2 = df_dict[dom]
    # Initializing mask to True ensures the mask exists and acts as a placeholder
    mask = True
    filter_rows = True

    # Apply conditions to mask
    if dept:
        mask = mask & (df_dict[dom]['dept'] == dept)
    if cat and (cat != 'Clothing'):
        mask = mask & (df_dict[dom]['catname1'] == cat)
    elif cat == 'Clothing':
        # catlist = ['Activewear', 'Bottoms', 'Outerwear', 'Dresses', 'Tops']
        # mask = mask & (df_dict[dom]['catname1'].isin(catlist))
        filter_rows = False
    if brand_prefix:
        mask = mask & (df_dict[dom]['brandletter'] == brand_prefix)

    # Final filtering based on the updated mask
    
    if filter_rows:
        filter_idx = np.where(mask)[0]
        df2 = df_dict[dom][mask]
    else:
        filter_idx = clothing_idx[dom]
        df2 = clothing_df_dict[dom]
    
    #df2 = df2.reset_index(drop = True)
    # Personalized RR Calc
    cust_rr_units = float(pw['ps_aff']['RR'])
    
    sum_of_features_coefficients = df2['pre_load_rr_features'] + rr_coef.iloc[0, 2] * ( (cust_rr_units - df2['cust_rr_units_mean']) / df2['cust_rr_units_std'] )
    adjusted_proj_rr = np.array(( 1 / (1 + np.power(math.e, -sum_of_features_coefficients)) ) * df2['calibration_ratio'])
    finalsale_idx = np.where(np.array(df2['finalsale']) == 1)[0]
    adjusted_proj_rr[finalsale_idx] = 0.
    
    # Size availability / affinity calculations
    sa_key_arr = 100 * cat_arr[dom][filter_idx] + size_indices[dom][filter_idx]
    sa_key_arr2 = 100 * cat_arr[dom][filter_idx] + size_indices2[dom][filter_idx]
    print(sa_key_arr.shape, sa_key_arr2.shape)
    shoes_aff_vec = np.array([float(s) for s in pw['ps_sho'].values()])
    shoe_aff_1 = np.matmul(shoe_sa_arr[dom][filter_idx] , shoes_aff_vec)
    shoe_aff_2 = np.matmul(shoe_total_sa_arr[dom][filter_idx], shoes_aff_vec)
    shoe_aff_scores = np.round(shoe_aff_1 + .6 * (shoe_aff_2 - shoe_aff_1), decimals = 3)
    
    ps_dict = {}
    for item_type in ['clo', 'bot', 'sho', 'oth']:

        cat_code = 100 * type_idx_dict.get(item_type, 0)
        if item_type != 'oth':
            # # added default fallback per category
            # if not pw[f'ps_{item_type}']:
            #     pw[f'ps_{item_type}'] = default_pw[pw[f'ps_{item_type}']]
            w_dict = pw[f'ps_{item_type}']
            for key in w_dict.keys():
                if item_type == 'sho':
                    size_code = shoe_sa_dict.get(key, -1)  # Use the dictionary for lookup
                else:
                    size_code = size_availability_list.index(key)

                if size_code != -1:  # Ensure the size_code is valid
                    ps_dict[cat_code + size_code] = float(w_dict[key])
            for size_code in range(32):
                if cat_code + size_code not in ps_dict.keys():
                    ps_dict[cat_code + size_code] = 0
        else:
            for size_code in range(32):
                ps_dict[cat_code + size_code] = 0

    psa = np.vectorize(ps_dict.__getitem__)(sa_key_arr)
    psa2 = np.vectorize(ps_dict.__getitem__)(sa_key_arr2)
    nonshoes_aff_scores = psa + (psa2 - psa) * .6
    nonshoes_aff_scores = np.round(nonshoes_aff_scores, decimals=3)
    
    w_sa_final = np.round(df2['sa_weight'] + nonshoes_aff_scores + shoe_aff_scores, decimals=3)
    w_psa = nonshoes_aff_scores + shoe_aff_scores
    
    # Other affinities
    aff_list = ['FP', 'MD','LPP','HPP', 'HPP+LPP', 'Old', 'Young', 'Young+Old']
    aff_mat = np.ones(len(aff_list))
    if pw['ps_aff']:
        for aff, weight in pw['ps_aff'].items():
            if aff in aff_list:
                aff_mat[aff_list.index(aff)] = weight 
    
    values = aff_values[dom][filter_idx]    
    w_aff_md = values[:, :2]@aff_mat[:2].T
    w_aff_lpp = values[:, 2:5]@aff_mat[2:5].T
    w_aff_age = values[:, 5:8]@aff_mat[5:8].T
    # df2['w_aff_md'] = w_aff_md
    # df2['w_aff_lpp'] = w_aff_lpp
    # df2['w_aff_age'] = w_aff_age
    
    if pw['lv'] is None or pw['lv'] == 'None':
        new_prods = np.ones(df2.shape[0])
    else:
        new_prods = (df2['readydate'] >= pw['lv']).astype(float)
    if pw['visitorsegment'] == 'F':
        const = 0.1
    else:
        const = 0.25    
    w_prods_new = new_prods + const * (1-new_prods)
    # df2['w_prods_new'] = w_prods_new
    
    # Calculate final combined score
    final_weight = (w_sa_final 
                       * w_aff_md
                       * w_aff_lpp
                       * w_aff_age
                       * w_prods_new
                       * df2['boost_factor']
                      )
    if rr:
        final_weight = final_weight / df2['proj_kr'] * (1 - adjusted_proj_rr) # prob(keep)
    # df2['final_weight'] = final_weight
    final_rpi_score = np.array(df2['capped_netrev_on_median_imp'] * final_weight)
    # df2['final_impression'] = final_rpi_score
    # result_df = df2.sort_values(by = 'final_impression', ascending = False).reset_index(drop = True)
    
    prods = list(df2['product'])
    res_idx = np.argsort(-final_rpi_score) 
    res = [prods[i] for i in res_idx]
    results = ','.join(res)
    
    # personal_aff = pw['ps_aff']
    # upload_df = pd.DataFrame({"browserid": [browserid], 'category': [cat], 'brand': [brand_prefix],'results': [','.join(res[:1000])], 'personal_brand_fpmd': str(personal_aff)})
    # conn.uploadDftoRS(upload_df, '/workspace/sort_result/result.csv',  # local file path to save csv
    #                     'prod_recs/plp.csv',  # filepath on s3, do whatever since it's temporary
    #                     'bi_work', 'jr_api_plp_sort',
    #                     ACCESS_KEY, SECRET_KEY, truncate=True)
    # upload_df2 = result_df[['code', 'lpp', 'hpp', 'markdown', 'full_price', 'young', 'old', 'young+old','size_availability2', 'w_sa_final',
    #     'w_aff_md', 'w_aff_lpp','w_aff_age', 'w_prods_new','final_weight','netrev_on_median_imp', 'final_impression', 'sa_weight','w_psa', 'boost_factor']]
    # conn.uploadDftoRS(upload_df2[['code', 'lpp', 'hpp', 'markdown', 'full_price', 'young', 'old', 'young+old','size_availability2', 'w_sa_final',
    #     'w_aff_md', 'w_aff_lpp','w_aff_age', 'w_prods_new','final_weight','netrev_on_median_imp', 'final_impression', 'sa_weight','w_psa', 'boost_factor']], '/workspace/sort_result/result2.csv',  # local file path to save csv
    #                     'prod_recs/plp_prods.csv',  # filepath on s3, do whatever since it's temporary
    #                     'bi_work', 'jr_api_plp_sort_prods',
    #                     ACCESS_KEY, SECRET_KEY, truncate=True)
    
    return results


app = FastAPI()
@app.on_event("startup")
async def startup_event():
    logger.info("FastAPI app started")

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("FastAPI app shutdown")
    
@app.get("/plp_sort/", response_class = PlainTextResponse)
def home_page(cat: str = Query(None, alias="Category"), 
              brand: str = Query(None, alias="Brand"),
              dept: str = Query(None, alias="Dept"),
              browserid: str = Query('none', alias='BrowserID')):
    # Convert inputs to proper case or handle them as case-insensitive
    # logging.info('request start')
    cat = cat.title() if cat else 'Clothing'
    brand = brand.upper() if brand else brand
    browserid = browserid if browserid else browserid
    dept = dept.upper() if dept else 'F'
    results = get_sort(df_dict, cat_arr, size_indices, size_indices2, shoe_sa_arr, shoe_total_sa_arr,aff_values, browserid, dept, cat, brand_prefix = brand)
    logging.info('finished sort')
    return results

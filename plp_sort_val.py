import os, sys
sys.path.insert(0, '/bi-python-tools')

import pandas as pd
import numpy as np
import Database_Connection_Class as db
import general_tools as gt
import requests
from urllib.request import urlretrieve
from datetime import datetime
import time
import pickle
import json
import math
creds = {'bi_admin':'work011DAY'}
conn = db.DBConnection(mars_cred=creds, 
                         venus_cred=creds,
                         panda_cred=creds,  
                         rs_cred=creds,
                          rs_sslmode = 'verify-ca')
ACCESS_KEY = '********************'
SECRET_KEY = '22ueDKaakUr0O3/w/Sln60v+O7w1EuNL7Nx6PhLv'
from common_functions import *
import gunicorn
import uvicorn
import logging 
logging.basicConfig(
    filename='/workspace/logs/plp_sort_log.txt',
    format='%(asctime)s %(levelname)-8s %(message)s',
    level=logging.INFO,
    datefmt='%Y-%m-%d %H:%M:%S')

from fastapi import FastAPI, Request, Query, HTTPException
# import httpx
from fastapi.responses import PlainTextResponse
from redis import Redis

from datetime import date
import pytz

with open('/workspace/pickle_files/prod_emb_date.pkl', 'rb') as handle:
    prod_emb_date = pickle.load(handle)

default_pw = {'lv': '1970-1-1',
 'ps_clo': {'No L': '0',
  'No L M': '0',
  'No L M S': '0',
  'No L M S XS': '0',
  'No L M XS': '0',
  'No L S': '0',
  'No L S XS': '0',
  'No L XS': '0',
  'No M': '0',
  'No M S': '0',
  'No M S XS': '0',
  'No M XS': '0',
  'No S': '0',
  'No S XS': '0',
  'No XL': '0',
  'No XL L': '0',
  'No XL L M': '0',
  'No XL L M S': '0',
  'No XL L M XS': '0',
  'No XL L S': '0',
  'No XL L S XS': '0',
  'No XL L XS': '0',
  'No XL M': '0',
  'No XL M S': '0',
  'No XL M S XS': '0',
  'No XL M XS': '0',
  'No XL S': '0',
  'No XL S XS': '0',
  'No XL XS': '0',
  'No XS': '0',
  'No XL L M S XS': '0'},
 'ps_bot': {'No L': '0',
  'No L M': '0',
  'No L M S': '0',
  'No L M S XS': '0',
  'No L M XS': '0',
  'No L S': '0',
  'No L S XS': '0',
  'No L XS': '0',
  'No M': '0',
  'No M S': '0',
  'No M S XS': '0',
  'No M XS': '0',
  'No S': '0',
  'No S XS': '0',
  'No XL': '0',
  'No XL L': '0',
  'No XL L M': '0',
  'No XL L M S': '0',
  'No XL L M XS': '0',
  'No XL L S': '0',
  'No XL L S XS': '0',
  'No XL L XS': '0',
  'No XL M': '0',
  'No XL M S': '0',
  'No XL M S XS': '0',
  'No XL M XS': '0',
  'No XL S': '0',
  'No XL S XS': '0',
  'No XL XS': '0',
  'No XS': '0',
  'No XL L M S XS': '0'},
 'ps_sho': {'No 5': '0',
  'No 5.5': '0',
  'No 6': '0',
  'No 6.5': '0',
  'No 7': '0',
  'No 7.5': '0',
  'No 8': '0',
  'No 8.5': '0',
  'No 9': '0',
  'No 9.5': '0',
  'No 10': '0',
  'No 10.5': '0',
  'No 11': '0',
  'No 11.5': '0',
  'No 12': '0',
  'No 12.5': '0',
  'No 13': '0'},
 'ps_aff': {'FP': '1',
  'MD': '1',
  'HPP': '1',
  'LPP': '1',
  'Young': '1',
  'Old': '1',
  'Young+Old': '1',
  'FemmeGirl': '1',
  'CoolGirl': '1',
  'SophisticatedChic': '1',
  'Old2': '1',
  'Young2': '1',
  'RR': '1'},
 'domestic': 1, 
 'visitorsegment': ''}
size_availability_list = [
    'Full Stock', 'No L', 'No L M', 'No L M S', 'No L M S XS', 'No L M XS', 'No L S',
    'No L S XS', 'No L XS', 'No M', 'No M S', 'No M S XS', 'No M XS', 'No S', 'No S XS',
    'No XL', 'No XL L', 'No XL L M', 'No XL L M S', 'No XL L M XS', 'No XL L S',
    'No XL L S XS', 'No XL L XS', 'No XL M', 'No XL M S', 'No XL M S XS', 'No XL M XS',
    'No XL S', 'No XL S XS', 'No XL XS', 'No XS', 'No XL L M S XS'
]
size_availability_dict = {v: i for i, v in enumerate(size_availability_list)}
shoe_sa_list = ['No 5',
'No 5.5',
'No 6',
'No 6.5',
'No 7',
'No 7.5',
'No 8',
'No 8.5',
'No 9',
'No 9.5',
'No 10',
'No 10.5',
'No 11',
'No 11.5',
'No 12',
'No 12.5',
'No 13']
shoe_sa_dict = {size: idx for idx, size in enumerate(shoe_sa_list)}  
type_idx_dict = {'clo' : 1, 'bot' : 2, 'sho' : 3}
catname_dict = {'Clothing' : 1, 'Bottoms' : 2, 'Shoes' : 3, 'Others' : 0}

rr_coef = pd.read_pickle('/workspace/pickle_files/rr_coef.pkl')
with open('/workspace/pickle_files/df_dict.pkl', 'rb') as handle:
    df_dict = pickle.load(handle)
# with open('/workspace/pickle_files/product_to_index', 'rb') as handle:
#     product_to_index = pickle.load(handle)
with open('/workspace/pickle_files/shoe_sa_arr.pkl', 'rb') as handle:
    shoe_sa_arr = pickle.load(handle)
with open('/workspace/pickle_files/shoe_total_sa_arr.pkl', 'rb') as handle:
    shoe_total_sa_arr = pickle.load(handle)
with open('/workspace/pickle_files/size_indices.pkl', 'rb') as handle:
    size_indices = pickle.load(handle)
with open('/workspace/pickle_files/size_indices2.pkl', 'rb') as handle:
    size_indices2 = pickle.load(handle)
with open('/workspace/pickle_files/shoe_sa_arr.pkl', 'rb') as handle:
    shoe_sa_arr = pickle.load(handle)
with open('/workspace/pickle_files/shoe_total_sa_arr.pkl', 'rb') as handle:
    shoe_total_sa_arr = pickle.load(handle)
with open('/workspace/pickle_files/size_indices.pkl', 'rb') as handle:
    size_indices = pickle.load(handle)
with open('/workspace/pickle_files/size_indices2.pkl', 'rb') as handle:
    size_indices2 = pickle.load(handle)
with open('/workspace/pickle_files/size_indices2.pkl', 'rb') as handle:
    size_indices2 = pickle.load(handle)
with open('/workspace/pickle_files/cat_arr.pkl', 'rb') as handle:
    cat_arr = pickle.load(handle)
with open('/workspace/pickle_files/aff_values.pkl', 'rb') as handle:
    aff_values = pickle.load(handle)

with open('/workspace/pickle_files/clothing_df_dict.pkl', 'rb') as handle:
    clothing_df_dict = pickle.load(handle)
with open('/workspace/pickle_files/clothing_size_indices.pkl', 'rb') as handle:
    clothing_size_indices = pickle.load(handle)
with open('/workspace/pickle_files/clothing_size_indices2.pkl', 'rb') as handle:
    clothing_size_indices2 = pickle.load(handle)
with open('/workspace/pickle_files/clothing_cat_arr.pkl', 'rb') as handle:
    clothing_cat_arr = pickle.load(handle)
with open('/workspace/pickle_files/clothing_aff_values.pkl', 'rb') as handle:
    clothing_aff_values = pickle.load(handle)
with open('/workspace/pickle_files/clothing_dept_df_dict.pkl', 'rb') as handle:
    clothing_dept_df_dict = pickle.load(handle)
with open('/workspace/pickle_files/clothing_dept_idx_dict.pkl', 'rb') as handle:
    clothing_dept_idx_dict = pickle.load(handle)

with open('/workspace/pickle_files/prod_embs.pkl', 'rb') as f:
    prod_embs = pickle.load(f)

with open('/workspace/pickle_files/prod_rr_cust_bucket.pkl', 'rb') as f:
    prod_rr_cust_bucket = pickle.load(f)

with open("/workspace/pickle_files/mlb_and_sparse.pkl", "rb") as f:
    mlb_and_sparse = pickle.load(f)


# remove unnecessary columns  
columns = ['product','Clothing','dept','catname1','brandletter','capped_netrev_on_median_imp','finalsale', 'readydate','proj_kr',
           'sa_weight','calibration_ratio','boost_factor', 'maincat', 'subcat1', 'subcat2', 
           'cust_rr_units_bucket_0', 'cust_rr_units_bucket_0_001',
            'cust_rr_units_bucket_0_3', 'cust_rr_units_bucket_0_399',
            'cust_rr_units_bucket_0_477', 'cust_rr_units_bucket_0_597',
            'cust_rr_units_bucket_0_711', 'cust_rr_units_bucket_0_825', 'size_availability2']
df_dict['dom'] = df_dict['dom'].reset_index(drop = True)[columns]
df_dict['intl'] = df_dict['intl'].reset_index(drop = True)[columns]


def get_scores(result_prods, cat_arr, size_indices, size_indices2, shoe_sa_arr, shoe_total_sa_arr,aff_values, browserid = 'none', dept = 'F', cat = None, subcat1 = None, subcat2 = None, brand_prefix = None, siteflag = 'R', rr = True,  num_prec_elig = 100, prec_share = 0.15, prec_min_rpi_quantile = 0.5):
    
    # get customer's affinities
    try:
        pw = get_affinity(browserid, siteflag)
    except: 
        pw = default_pw
    if pw is None or len(pw) == 0:
        pw = default_pw
    try:
        dom = pw['domestic']
    except KeyError:
        dom = 1
    if dom == 1:
        dom = 'dom'
    else:
        dom = 'intl'
    
    for key in ['clo', 'bot', 'sho', 'aff']:
        if not pw[f'ps_{key}']:
            pw[f'ps_{key}'] = default_pw[f'ps_{key}']

    result_prods = result_prods.split(',')
    result_prods_df = pd.DataFrame({'product': result_prods})
    filter_idx = df_dict[dom].index[df_dict[dom]['product'].isin(result_prods)].tolist()
    df2 = result_prods_df.merge(df_dict[dom], on = 'product', how = 'inner')
    
    # # Apply conditions to mask
    # mask = True
    # if cat !='Clothing':
    #     dept_mask = (df_dict[dom]['dept'] == dept) if dept else True
    #     # cat_mask = df_dict[dom]['maincat'].apply(lambda x: cat in x) if cat and (cat!= 'Clothing') else True
    #     # subcat1_mask = df_dict[dom]['subcat1'].apply(lambda x: subcat1 in x) if subcat1 else True
    #     # subcat2_mask = df_dict[dom]['subcat2'].apply(lambda x: subcat2 in x) if subcat2 else True
    #     if cat and cat != 'Clothing':
    #         cat_idx = mlb_and_sparse['mlb_maincat'][dom].classes_.tolist().index(cat)
    #         cat_mask = mlb_and_sparse['maincat_sparse'][dom][:, cat_idx] == 1
    #     else:
    #         cat_mask = True
    #     if subcat1:
    #         subcat1_idx = mlb_and_sparse['mlb_subcat1'][dom].classes_.tolist().index(subcat1)
    #         subcat1_mask = mlb_and_sparse['subcat1_sparse'][dom][:, subcat1_idx] == 1
    #     else:
    #         subcat1_mask = True
    #     if subcat2:
    #         subcat2_idx = mlb_and_sparse['mlb_subcat2'][dom].classes_.tolist().index(subcat2)
    #         subcat2_mask = mlb_and_sparse['subcat2_sparse'][dom][:, subcat2_idx] == 1
    #     else:
    #         subcat2_mask = True
    #     brand_mask = (df_dict[dom]['brandletter'] == brand_prefix) if brand_prefix else True
    #     combined_mask = dept_mask & cat_mask & brand_mask & subcat1_mask & subcat2_mask
    #     filter_idx = np.where(combined_mask)[0]
    #     df2 = df_dict[dom][combined_mask]
    # else:
    #     filter_idx = clothing_dept_idx_dict[dept][dom]
    #     df2 = clothing_dept_df_dict[dept][dom]
    # Personalized RR Calc
    cust_rr_units = float(pw['ps_aff']['RR'])
    # add shuya's new logics for rr 
    cust_rr_units_bucket = prod_rr_cust_bucket[
            (prod_rr_cust_bucket["lowerbound_ge"] <= cust_rr_units) &
            (prod_rr_cust_bucket["higherbound_lt"] > cust_rr_units)
        ]['cust_rr_units_bucket'].values[0]
    bucket_colname = 'cust_rr_units_bucket_'+ str(cust_rr_units_bucket).replace('.', '_')
    # df2 = df2[df2['cust_rr_units_bucket'] == cust_rr_units_bucket] - instead of slicing a dataframe, just save the indices as a numpy array
    
    # sum_of_features_coefficients = df2['pre_load_rr_features'] + rr_coef.iloc[0, 2] * ( (cust_rr_units - df2['cust_rr_units_mean']) / df2['cust_rr_units_std'] )
    # adjusted_proj_rr = np.array(( 1 / (1 + np.power(math.e, -sum_of_features_coefficients)) ) * df2['calibration_ratio'])
    adjusted_proj_rr = df2[bucket_colname]
    finalsale_idx = np.where(np.array(df2['finalsale']) == 1)[0]
    adjusted_proj_rr.iloc[finalsale_idx] = 0.
    
    df2['adjusted_proj_rr'] = adjusted_proj_rr
    
    # Size availability / affinity calculations
    sa_key_arr = 100 * cat_arr[dom][filter_idx] + size_indices[dom][filter_idx]
    sa_key_arr2 = 100 * cat_arr[dom][filter_idx] + size_indices2[dom][filter_idx]
    loop_list = ['clo', 'bot', 'oth']
    if cat != 'Clothing':
        shoes_aff_vec = np.array([float(s) for s in pw['ps_sho'].values()])
        shoe_aff_1 = np.matmul(shoe_sa_arr[dom][filter_idx] , shoes_aff_vec)
        shoe_aff_2 = np.matmul(shoe_total_sa_arr[dom][filter_idx], shoes_aff_vec)
        shoe_aff_scores = shoe_aff_1 + .6 * (shoe_aff_2 - shoe_aff_1)
        loop_list.append('sho')
    ps_dict = {}
    # for item_type in ['clo', 'bot', 'sho', 'oth']:
    for item_type in loop_list:

        cat_code = 100 * type_idx_dict.get(item_type, 0)
        if item_type != 'oth':
            # # added default fallback per category
            # if not pw[f'ps_{item_type}']:
            #     pw[f'ps_{item_type}'] = default_pw[pw[f'ps_{item_type}']]
            w_dict = pw[f'ps_{item_type}']
            for key in w_dict.keys():
                if (item_type == 'sho') & (cat != 'Clothing'):
                    size_code = shoe_sa_dict.get(key, -1)  # Use the dictionary for lookup
                else:
                    size_code = size_availability_list.index(key)

                if size_code != -1:  # Ensure the size_code is valid
                    ps_dict[cat_code + size_code] = float(w_dict[key])
            for size_code in range(32):
                if cat_code + size_code not in ps_dict.keys():
                    ps_dict[cat_code + size_code] = 0
        else:
            for size_code in range(32):
                ps_dict[cat_code + size_code] = 0

    psa = np.vectorize(ps_dict.__getitem__)(sa_key_arr)
    psa2 = np.vectorize(ps_dict.__getitem__)(sa_key_arr2)
    nonshoes_aff_scores = psa + (psa2 - psa) * .6
    nonshoes_aff_scores = nonshoes_aff_scores
    
    if cat != 'Clothing':
        w_sa_final = df2['sa_weight'] + nonshoes_aff_scores + shoe_aff_scores
    else:
        w_sa_final = df2['sa_weight'] + nonshoes_aff_scores
    df2['psa'] = psa
    df2['psa2'] = psa2
    df2['w_sa_final'] = w_sa_final
    
    # Other affinities
    aff_list = ['FP', 'MD','LPP','HPP', 'HPP+LPP', 'Old', 'Young', 'Young+Old']
    aff_mat = np.ones(len(aff_list))
    if pw['ps_aff']:
        for aff, weight in pw['ps_aff'].items():
            if aff in aff_list:
                aff_mat[aff_list.index(aff)] = weight 
    
    values = aff_values[dom][filter_idx]    
    w_aff_md = values[:, :2]@aff_mat[:2].T
    w_aff_lpp = values[:, 2:5]@aff_mat[2:5].T
    w_aff_age = values[:, 5:8]@aff_mat[5:8].T

    if pw['lv'] is None or pw['lv'] == 'None':
        new_prods = np.ones(df2.shape[0])
    else:
        new_prods = (df2['readydate']>=pd.to_datetime(pw['lv'])).astype(float) 
    if pw['visitorsegment'] == 'F':
        const = 0.1
    else:
        const = 0.25    
    w_prods_new = new_prods + const * (1-new_prods)
    df2['w_prods_new'] = w_prods_new
    
    # Calculate final combined score
    final_weight = (w_sa_final 
                       * w_aff_md
                       * w_aff_lpp
                       * w_aff_age
                       * w_prods_new
                       * df2['boost_factor']
                      )
    if rr:
        final_weight = final_weight / df2['proj_kr'] * (1 - adjusted_proj_rr) # prob(keep)
    final_rpi_score = np.array(df2['capped_netrev_on_median_imp'] * final_weight)
    df2['w_aff_md'] = w_aff_md
    df2['w_aff_lpp'] = w_aff_lpp
    df2['w_aff_age'] = w_aff_age
    df2['final_weight'] = final_weight
    df2['final_score'] = final_rpi_score
    
    prods = np.array(df2['product'])
    # res_idx = np.argsort(-final_rpi_score) 
    if num_prec_elig * prec_share >= 1:
        min_rpi = df2['capped_netrev_on_median_imp'].quantile(prec_min_rpi_quantile)
        inelig_idx = np.where(final_rpi_score < min_rpi)[0]
        redis_user_factors = raw_redis_db.get(f'pr_emb_{browserid}_{prod_emb_date}')
        user_factors = np.frombuffer(redis_user_factors, dtype=np.float16)
       
        prod_rec_score = np.matmul(prod_embs[dom], user_factors)[filter_idx]
        prod_rec_score = 100 * (prod_rec_score - prod_rec_score.min())
        prod_rec_score[inelig_idx] = 0
        #prod_rec_score = (prod_rec_score
        #               / df2['proj_kr'] * (1 - adjusted_proj_rr) # prob(keep)
        #              )
        df2['prod_rec_score'] = prod_rec_score
        # prec_spots = np.where(np.random.uniform(0,1,num_prec_elig) <= prec_share)[0]
        # num_prec_spots = len(prec_spots)
        # nonprec_spots = np.append(np.array(list(set(range(num_prec_elig)) - set(prec_spots))),
        #                           np.array(list(range(num_prec_elig,len(final_rpi_score)+num_prec_spots))))
       
        # prec_res_idx = np.argsort(-prod_rec_score)[:num_prec_spots]
       
        # augmented_res_idx = np.zeros(shape = len(final_rpi_score) + num_prec_spots, dtype = int)
        # augmented_res_idx[prec_spots] = prec_res_idx
        # augmented_res_idx[nonprec_spots] = res_idx
       
        # res_idx = pd.Series(augmented_res_idx).drop_duplicates().to_list()

    # For frequent users: Insert ATC -> hearted/wishlisted -> viewed PDP items
    if pw['visitorsegment'] == 'F':
        insert_positions = [1, 3, 5, 7, 9]

        atc_hearted_viewed_items = pw['atchv'].split(',')  # List of additional product indices to insert
        # check products that sufficies the instock condition and filter
        # below method doesnt reserve the order of atc items 
        # intersection_indices = np.where(df_dict[dom]['product'].isin(set(atc_hearted_viewed_items)))[0]
        # final_indices = np.intersect1d(intersection_indices, res_idx) 
        product_to_index = {product: idx for idx, product in enumerate(prods)}
        final_indices = [
                product_to_index[product]
                for product in atc_hearted_viewed_items
                if product in product_to_index
            ]
        # if len(final_indices) > 0:
        #     additional_products = final_indices[:min(5, len(final_indices))]

        #     # Create a mask for insertion positions
        #     insert_mask = np.zeros(len(augmented_res_idx) + len(additional_products), dtype=bool)
        #     insert_mask[np.array(insert_positions[:len(additional_products)])] = True

        #     # Create the final augmented index array
        #     new_augmented_res_idx = np.zeros(len(augmented_res_idx) + len(additional_products), dtype=int)

        #     # Fill in products at insertion positions
        #     new_augmented_res_idx[insert_mask] = additional_products

        #     # Fill remaining positions
        #     remaining_positions = ~insert_mask
        #     new_augmented_res_idx[remaining_positions] = augmented_res_idx[:remaining_positions.sum()]

        #     # Deduplicate and finalize
        #     res_idx = pd.Series(new_augmented_res_idx).drop_duplicates().to_list()
    
    # res = prods[res_idx].tolist()
    # results = ','.join(res)
    
    prod_val = df2[['product', 'sa_weight', 'psa', 'psa2', 'w_sa_final', 'w_aff_md', 'w_aff_lpp', 'w_aff_age','w_prods_new', 'boost_factor','final_weight', 'capped_netrev_on_median_imp', 'final_score', 'prod_rec_score', 'adjusted_proj_rr', 'proj_kr', 'size_availability2']]
    prod_val['browserid'] = [browserid] * len(prod_val)

    conn.uploadDftoRS(prod_val[['product', 'sa_weight', 'psa', 'psa2', 'w_sa_final', 'w_aff_md', 'w_aff_lpp', 'w_aff_age','w_prods_new', 'boost_factor','final_weight', 'capped_netrev_on_median_imp', 'final_score', 'prod_rec_score', 'browserid', 'adjusted_proj_rr', 'proj_kr', 'size_availability2']], '/workspace/sort_result/result2.csv',  # local file path to save csv
                        'prod_recs/plp_prods.csv',  # filepath on s3, do whatever since it's temporary
                        'bi_work', 'jr_api_plp_sort_prods',
                        ACCESS_KEY, SECRET_KEY, truncate=True)
    


result_prods = 'LCDE-WD945,NBDR-WD2906,NBDR-WD3290,NJAC-WD57,LCDE-WD942,LIKR-WD805,LCDE-WD939,BARD-WD840,MALR-WD1483,LOVF-WD4219,NBDR-WD3296,LCDE-WD940,LCDE-WD941,MALR-WD1482,AWAR-WD57,LCDE-WD943,LCDE-WD944,PGEO-WD31,ULLA-WD524,OSSE-WD48,JSKI-WD484,JSKI-WD482,JSKI-WD478,JSKI-WD480,SBNA-WD267,CLNR-WD151,ZIMM-WD557,ZIMM-WD555,ZIMM-WD554,ZIMM-WD553,JSKI-WD479,ZIMM-WD551,ZIMM-WD550,ZIMM-WD549,SDYS-WD219,ZIMM-WD548,ZIMM-WD552,UAMR-WD228,LBOK-WD9,PGEO-WD29,PGIR-WD83,ROFR-WD896,OSSE-WD46,SERR-WD230,FORL-WD1333,FORL-WD1332,FREE-WD2922,VINCE-WD158,MPAL-WD44,ZIMM-WD570,MALR-WD1481,MALR-WD1480,TIRR-WI259,BARD-WD860,ONIR-WD15,PGEO-WD27,ZIMM-WD568,LOVF-WD4309,ASTR-WD647,CULG-WD374,GCDR-WD4,SPDW-WD2716,JSKI-WD481,ASTR-WD645,CULG-WD375,LBOK-WD8,PLOR-WD18,BARD-WD858,TWOL-WD28,CULG-WD376,BARD-WD859,ROFR-WD898,BARD-WD857,ASTR-WD648,BARD-WD856,GCDR-WD7,LAGR-WD232,RONR-WD745,ROCS-WD331,AMAN-WD1605,BLAN-WD28,RONR-WD747,TWOL-WD29,JORR-WD24,MOTO-WD541,UAMR-WD226,OFFR-WD65,LOVF-WD4305,UAMR-WD227,CULG-WD372,BARD-WD855,UAMR-WD225,ASTR-WD646,ROFR-WD900,ELLI-WD745,LIOR-WD109,SPDW-WD2715,SHON-WD553,JORR-WD23,SPDW-WD2718,AMAN-WD2048,AMAN-WD1348,NBDR-WD3274,AMAN-WD1845,HLSA-WD110,LOVF-WD3938,COEL-WD405,HLSA-WD124,FORL-WD845,KATR-WD401,ELLI-WD500,SHON-WD502,MELR-WD1127,NKAM-WD510,ATOR-WD116,MELR-WD357,NKIE-WD632'
get_scores(result_prods, cat_arr, size_indices, size_indices2, shoe_sa_arr, shoe_total_sa_arr, aff_values, browserid = 'avccuifOwErVbewfxNVxmsTSenJy3c', cat = 'Dresses')